{"name": "backend", "version": "1.0.0", "description": "Full-featured backend with authentication and email services", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "deploy": "node auto-deploy.js", "quick-start": "node auto-deploy.js", "one-click-deploy": "node auto-deploy.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:start:prod": "pm2 start ecosystem.config.js --env production", "pm2:start:dev": "pm2 start ecosystem.config.js --env development", "pm2:stop": "pm2 stop faceapp-backend", "pm2:restart": "pm2 restart faceapp-backend", "pm2:reload": "pm2 reload faceapp-backend", "pm2:delete": "pm2 delete faceapp-backend", "pm2:logs": "pm2 logs faceapp-backend", "pm2:monit": "pm2 monit", "pm2:status": "pm2 status", "pm2:flush": "pm2 flush", "pm2:reset": "pm2 reset faceapp-backend", "db:indexes": "node scripts/createIndexes.js", "db:indexes:force": "node scripts/createIndexes.js --force", "monitor": "node scripts/monitor.js report", "monitor:pm2": "node scripts/monitor.js pm2", "monitor:mongodb": "node scripts/monitor.js mongodb", "monitor:watch": "node scripts/monitor.js monitor", "setup:production": "npm run db:indexes && npm run pm2:start:prod && npm run autostart:setup", "setup:development": "npm run db:indexes && npm run pm2:start:dev", "autostart:setup": "node scripts/setup-autostart.js setup", "autostart:check": "node scripts/setup-autostart.js check", "autostart:disable": "node scripts/setup-autostart.js disable", "windows:service:install": "node install-windows-service.js install", "windows:service:uninstall": "node install-windows-service.js uninstall", "windows:service:status": "node install-windows-service.js status", "windows:service:start": "net start \"Face Analysis API\"", "windows:service:stop": "net stop \"Face Analysis API\"", "windows:startup": "start-faceapp.bat", "logs:create": "mkdir -p logs", "logs:clear": "rm -rf logs/*.log", "health": "curl -f http://localhost:3001/api/health || exit 1", "performance": "npm run monitor && npm run pm2:status", "deploy:bash": "bash auto-deploy.sh", "deploy:windows": "auto-deploy.bat", "deploy:auto": "node auto-deploy.js"}, "keywords": ["nodejs", "express", "mongodb", "jwt", "authentication"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.24.1", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cloudinary": "^2.7.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "ioredis": "^5.6.1", "jimp": "^0.22.12", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "multer": "^2.0.1", "node-cache": "^5.1.2", "nodemailer": "^6.9.4", "redis": "^5.5.6", "sharp": "^0.34.2"}, "devDependencies": {"nodemon": "^3.0.1"}}