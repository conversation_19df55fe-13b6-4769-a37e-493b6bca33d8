# 🌐 Face App API Documentation - Complete Guide

## 🚀 Quick Start

**Base URL:** `https://faceapp-ttwh.onrender.com/api`

```javascript
const API_BASE_URL = 'https://faceapp-ttwh.onrender.com/api';
const authHeaders = (token) => ({
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${token}`
});
```

---

## 🔐 Authentication APIs

### 1. Register User
**POST** `/api/auth/register`
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>", 
  "password": "MyPassword123",
  "gender": "male"
}
```
**Response (201):**
```json
{
  "success": true,
  "message": "User registered successfully. Please check your email for the verification OTP.",
  "user": {
    "id": "64f1234567890abcdef12345",
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "isEmailVerified": false
  }
}
```

### 2. Verify OTP
**POST** `/api/auth/verify-email-otp`
```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

### 3. Login
**POST** `/api/auth/login`
```json
{
  "email": "<EMAIL>",
  "password": "MyPassword123"
}
```
**Response (200):**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "64f1234567890abcdef12345",
    "name": "John Doe",
    "email": "<EMAIL>",
    "isEmailVerified": true
  }
}
```

### 4. Get Profile
**GET** `/api/auth/me` (Auth Required)

### 5. Logout
**POST** `/api/auth/logout` (Auth Required)

### 6. Resend OTP
**POST** `/api/auth/resend-verification`
```json
{
  "email": "<EMAIL>"
}
```

---

## 📸 Face Analysis APIs

### 1. Analyze Face
**POST** `/api/face/analyze-direct` (Auth Required)
```json
{
  "imageUrl": "https://res.cloudinary.com/dy1tsskkm/image/upload/v1234567890/face_analysis/abc123.jpg",
  "publicId": "face_analysis/abc123"
}
```
**Response (200):**
```json
{
  "success": true,
  "data": {
    "analysis": {
      "_id": "64f1234567890abcdef12345",
      "imageUrl": "https://res.cloudinary.com/dy1tsskkm/...",
      "faceDetected": true,
      "colors": {
        "dominantColors": ["#8B4513", "#D2691E"],
        "skinTone": "#F5DEB3",
        "eyeColor": "#8B4513",
        "hairColor": "#654321"
      },
      "facialFeatures": {
        "faceShape": "oval",
        "eyeShape": "almond"
      },
      "confidence": 0.95,
      "createdAt": "2024-01-01T12:00:00.000Z"
    }
  }
}
```

### 2. Get History
**GET** `/api/face/history?page=1&limit=10` (Auth Required)

### 3. Get Analysis
**GET** `/api/face/analysis/:id` (Auth Required)

### 4. Delete Analysis
**DELETE** `/api/face/analysis/:id` (Auth Required)

---

## 🎨 Color Recommendation APIs

### 1. Get Recommendations
**POST** `/api/face/analysis/:id/recommendations` (Auth Required)
```json
{
  "preferences": {
    "style": "professional",
    "occasion": "business"
  }
}
```
**Response (200):**
```json
{
  "success": true,
  "data": {
    "recommendations": [
      {
        "outfitName": "Professional Business Look",
        "shirt": {
          "color": "Navy Blue",
          "hex": "#000080",
          "reason": "Complements your warm skin tone"
        },
        "pants": {
          "color": "Charcoal Gray", 
          "hex": "#36454F",
          "reason": "Classic neutral"
        }
      }
    ],
    "colorPalette": {
      "bestColors": ["#000080", "#8B4513"],
      "avoidColors": ["#FF69B4", "#00FFFF"],
      "seasonalType": "Autumn"
    },
    "generalAdvice": "Your warm undertones work best with earth tones."
  }
}
```

### 2. Latest Recommendation
**GET** `/api/face/recommendations/latest` (Auth Required)

### 3. Recommendation History
**GET** `/api/face/recommendations/history?limit=10` (Auth Required)

### 4. Add Feedback
**POST** `/api/face/recommendations/:id/feedback` (Auth Required)
```json
{
  "rating": 5,
  "feedback": "Great recommendations!",
  "favoriteOutfits": [0, 1]
}
```
---
## 📤 File Upload APIs

### 1. Get Upload Signature
**POST** `/api/upload/mobile-signature` (Auth Required)
**Response (200):**
```json
{
  "success": true,
  "data": {
    "signature": "abc123def456...",
    "timestamp": **********,
    "cloudName": "dy1tsskkm",
    "apiKey": "123456789012345",
    "uploadUrl": "https://api.cloudinary.com/v1_1/dy1tsskkm/image/upload",
    "folder": "face_analysis"
  }
}
```
### 2. Upload Config
**GET** `/api/upload/config` (Auth Required)
---
## 📊 Monitoring APIs

### 1. Health Check
**GET** `/api/health` (No Auth)
```json
{
  "success": true,
  "message": "Server is running successfully",
  "uptime": 3600,
  "memory": {
    "used": "120 MB",
    "total": "512 MB"
  }
}
```
### 2. Simple Ping
**GET** `/ping` (No Auth)
## 💻 Complete Frontend Example
```html
<!DOCTYPE html>
<html>
<head>
    <title>Face App</title>
    <style>
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-group input, select { width: 100%; padding: 8px; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        .hidden { display: none; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Registration Form -->
        <div id="registerForm">
            <h2>Register</h2>
            <form onsubmit="handleRegister(event)">
                <div class="form-group">
                    <input type="text" id="name" placeholder="Full Name" required>
                </div>
                <div class="form-group">
                    <input type="email" id="email" placeholder="Email" required>
                </div>
                <div class="form-group">
                    <input type="password" id="password" placeholder="Password" required>
                </div>
                <div class="form-group">
                    <select id="gender" required>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                        <option value="prefer_not_to_say">Prefer not to say</option>
                    </select>
                </div>
                <button type="submit" class="btn">Register</button>
            </form>
            <div id="registerMessage"></div>
        </div>
        <!-- OTP Form -->
        <div id="otpForm" class="hidden">
            <h2>Verify Email</h2>
            <form onsubmit="handleOTP(event)">
                <div class="form-group">
                    <input type="text" id="otp" placeholder="Enter 6-digit OTP" maxlength="6" required>
                </div>
                <button type="submit" class="btn">Verify</button>
                <button type="button" class="btn" onclick="resendOTP()">Resend</button>
            </form>
            <div id="otpMessage"></div>
        </div>
        <!-- Login Form -->
        <div id="loginForm" class="hidden">
            <h2>Login</h2>
            <form onsubmit="handleLogin(event)">
                <div class="form-group">
                    <input type="email" id="loginEmail" placeholder="Email" required>
                </div>
                <div class="form-group">
                    <input type="password" id="loginPassword" placeholder="Password" required>
                </div>
                <button type="submit" class="btn">Login</button>
            </form>
            <div id="loginMessage"></div>
        </div>

        <!-- Dashboard -->
        <div id="dashboard" class="hidden">
            <h2>Dashboard</h2>
            <div id="userInfo"></div>
            <input type="file" id="faceImage" accept="image/*" onchange="uploadAndAnalyze(event)">
            <button class="btn" onclick="showHistory()">History</button>
            <button class="btn" onclick="logout()">Logout</button>
            <div id="results"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://faceapp-ttwh.onrender.com/api';
        let currentUser = null;
        let userEmail = '';

        const show = (id) => document.getElementById(id).classList.remove('hidden');
        const hide = (id) => document.getElementById(id).classList.add('hidden');
        const showMsg = (id, msg, isError = false) => {
            const el = document.getElementById(id);
            el.innerHTML = msg;
            el.className = isError ? 'error' : 'success';
        };

        async function handleRegister(e) {
            e.preventDefault();
            const data = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                gender: document.getElementById('gender').value
            };
            userEmail = data.email;

            try {
                const res = await fetch(`${API_BASE_URL}/auth/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await res.json();
                
                if (result.success) {
                    showMsg('registerMessage', result.message);
                    hide('registerForm');
                    show('otpForm');
                } else {
                    showMsg('registerMessage', result.message, true);
                }
            } catch (error) {
                showMsg('registerMessage', 'Registration failed', true);
            }
        }

        async function handleOTP(e) {
            e.preventDefault();
            const otp = document.getElementById('otp').value;

            try {
                const res = await fetch(`${API_BASE_URL}/auth/verify-email-otp`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: userEmail, otp })
                });
                const result = await res.json();
                
                if (result.success) {
                    showMsg('otpMessage', 'Verified successfully!');
                    setTimeout(() => {
                        hide('otpForm');
                        show('loginForm');
                    }, 2000);
                } else {
                    showMsg('otpMessage', result.message, true);
                }
            } catch (error) {
                showMsg('otpMessage', 'Verification failed', true);
            }
        }

        async function resendOTP() {
            try {
                const res = await fetch(`${API_BASE_URL}/auth/resend-verification`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: userEmail })
                });
                const result = await res.json();
                showMsg('otpMessage', result.message, !result.success);
            } catch (error) {
                showMsg('otpMessage', 'Resend failed', true);
            }
        }

        async function handleLogin(e) {
            e.preventDefault();
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            try {
                const res = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password })
                });
                const result = await res.json();
                
                if (result.success) {
                    localStorage.setItem('authToken', result.token);
                    currentUser = result.user;
                    hide('loginForm');
                    showDashboard();
                } else {
                    showMsg('loginMessage', result.message, true);
                }
            } catch (error) {
                showMsg('loginMessage', 'Login failed', true);
            }
        }

        function showDashboard() {
            show('dashboard');
            document.getElementById('userInfo').innerHTML = `<h3>Welcome, ${currentUser.name}!</h3>`;
        }

        async function uploadAndAnalyze(e) {
            const file = e.target.files[0];
            if (!file) return;

            const token = localStorage.getItem('authToken');
            
            try {
                // Get upload signature
                const sigRes = await fetch(`${API_BASE_URL}/upload/mobile-signature`, {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' }
                });
                const sigData = await sigRes.json();

                // Upload to Cloudinary
                const formData = new FormData();
                formData.append('file', file);
                formData.append('signature', sigData.data.signature);
                formData.append('timestamp', sigData.data.timestamp);
                formData.append('api_key', sigData.data.apiKey);
                formData.append('folder', sigData.data.folder);

                const uploadRes = await fetch(sigData.data.uploadUrl, {
                    method: 'POST',
                    body: formData
                });
                const uploadResult = await uploadRes.json();
                // Analyze face
                const analysisRes = await fetch(`${API_BASE_URL}/face/analyze-direct`, {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        imageUrl: uploadResult.secure_url,
                        publicId: uploadResult.public_id
                    })
                });
                const analysisResult = await analysisRes.json();
                if (analysisResult.success) {
                    displayResults(analysisResult.data.analysis);
                    getRecommendations(analysisResult.data.analysis._id);
                }
            } catch (error) {
                alert('Upload failed: ' + error.message);
            }
        }
        function displayResults(analysis) {
            document.getElementById('results').innerHTML = `
                <h3>Analysis Results</h3>
                <img src="${analysis.imageUrl}" style="max-width: 300px;">
                <p><strong>Face Shape:</strong> ${analysis.facialFeatures?.faceShape || 'N/A'}</p>
                <p><strong>Skin Tone:</strong> <span style="background: ${analysis.colors?.skinTone}; padding: 2px 8px; color: white;">${analysis.colors?.skinTone}</span></p>
                <p><strong>Confidence:</strong> ${(analysis.confidence * 100).toFixed(1)}%</p>
            `;
        }
        async function getRecommendations(analysisId) {
            const token = localStorage.getItem('authToken');
            
            try {
                const res = await fetch(`${API_BASE_URL}/face/analysis/${analysisId}/recommendations`, {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' },
                    body: JSON.stringify({ preferences: { style: 'professional' } })
                });
                const result = await res.json();

                if (result.success) {
                    displayRecommendations(result.data);
                }
            } catch (error) {
                console.error('Recommendations failed:', error);
            }
        }
        function displayRecommendations(data) {
            let html = `<h3>Color Recommendations</h3>
                       <p><strong>Seasonal Type:</strong> ${data.colorPalette.seasonalType}</p>
                       <p><strong>Advice:</strong> ${data.generalAdvice}</p>`;
            
            data.recommendations.forEach(outfit => {
                html += `<div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0;">
                           <h4>${outfit.outfitName}</h4>
                           <p><strong>Shirt:</strong> ${outfit.shirt.color} - ${outfit.shirt.reason}</p>
                           <p><strong>Pants:</strong> ${outfit.pants.color} - ${outfit.pants.reason}</p>
                         </div>`;
            });
            document.getElementById('results').innerHTML += html;
        }
        async function showHistory() {
            const token = localStorage.getItem('authToken');
            
            try {
                const res = await fetch(`${API_BASE_URL}/face/history`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                const result = await res.json();
                
                if (result.success) {
                    let html = '<h3>Analysis History</h3>';
                    result.data.analyses.forEach(analysis => {
                        html += `<div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0;">
                                   <img src="${analysis.imageUrl}" style="max-width: 100px;">
                                   <p>Date: ${new Date(analysis.createdAt).toLocaleDateString()}</p>
                                   <p>Confidence: ${(analysis.confidence * 100).toFixed(1)}%</p>
                                 </div>`;
                    });
                    document.getElementById('results').innerHTML = html;
                }
            } catch (error) {
                alert('Failed to get history');
            }
        }
        function logout() {
            localStorage.removeItem('authToken');
            currentUser = null;
            hide('dashboard');
            show('loginForm');
        }
        // Auto-login if token exists
        window.onload = function() {
            const token = localStorage.getItem('authToken');
            if (token) {
                // Verify token by getting user profile
                fetch(`${API_BASE_URL}/auth/me`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                }).then(res => res.json()).then(result => {
                    if (result.success) {
                        currentUser = result.data;
                        hide('registerForm');
                        showDashboard();
                    }
                });
            }
        };
    </script>
</body>
</html>
```
---
## 🛡️ Security & Rate Limits

| Endpoint | Limit | Window |
|----------|-------|--------|
| Global | 2000 | 15 min |
| Registration | 5 | 1 min |
| Login | 10 | 1 min |
| Face Analysis | 20 | 1 hour |
| Color Recommendations | 30 | 1 hour |

**Rate Limit Error (429):**
```json
{
  "success": false,
  "message": "Too many requests from this IP",
  "retryAfter": 900
}
```
---
## 🚨 Error Handling

**Auth Error (401):**
```json
{
  "success": false,
  "message": "Not authorized to access this route"
}
```
**Validation Error (400):**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "msg": "Password must be at least 6 characters",
      "param": "password"
    }
  ]
}
