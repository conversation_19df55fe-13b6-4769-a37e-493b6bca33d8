// Test script to verify the Gemini prompt formatting is clean
const geminiService = require('./services/geminiService');

// Mock face analysis data
const mockFaceData = {
  colors: {
    skinTone: { primary: 'Medium', hex: '#d4a574' },
    hairColor: { primary: '<PERSON> Brown', hex: '#3c2414' },
    eyeColor: { primary: 'Brown', hex: '#8b4513' },
    lipColor: { primary: 'Natural Pink', hex: '#dda0dd' }
  },
  facialFeatures: {
    faceShape: 'Oval',
    eyeShape: 'Almond',
    eyeDistance: 'Normal',
    eyebrowShape: 'Arched',
    noseShape: 'Straight',
    lipShape: 'Full'
  },
  confidence: 0.85
};

const mockUserProfile = { gender: 'female' };

console.log('🧪 Testing Gemini Prompt Formatting\n');

// Build the prompt
const prompt = geminiService.buildColorRecommendationPrompt(mockFaceData, mockUserProfile);

// Check for formatting issues
const checks = {
  'Bold formatting (**text**)': /\*\*.*?\*\*/.test(prompt),
  'Underline formatting (__text__)': /__.*?__/.test(prompt),
  'Emoji headers (🧑, 🎯, etc.)': /[🧑🎯🧠🚫✅🧾📌]/.test(prompt),
  'Backtick formatting (`text`)': /`[^`]+`/.test(prompt),
  'Contains requirements section': /REQUIREMENTS:/.test(prompt),
  'Contains output format section': /OUTPUT FORMAT:/.test(prompt),
  'Contains shoe restrictions': /SHOE COLOR RESTRICTIONS:/.test(prompt)
};

console.log('📋 Formatting Check Results:');
console.log('─'.repeat(50));

Object.entries(checks).forEach(([check, hasIssue]) => {
  const status = check.includes('Contains') ? 
    (hasIssue ? '✅ FOUND' : '❌ MISSING') :
    (hasIssue ? '❌ FOUND (PROBLEM)' : '✅ CLEAN');
  console.log(`${check}: ${status}`);
});

console.log('\n📝 Prompt Sample (first 300 chars):');
console.log('─'.repeat(50));
console.log(prompt.substring(0, 300) + '...');
console.log('─'.repeat(50));

// Check specific sections
const requirementsMatch = prompt.match(/REQUIREMENTS:([\s\S]*?)---/);
if (requirementsMatch) {
  const requirements = requirementsMatch[1];
  console.log('\n📋 Requirements Section Check:');
  console.log(`   Clean formatting: ${!/\*\*|__|`/.test(requirements) ? '✅ YES' : '❌ NO'}`);
  console.log(`   Contains "3 complete outfit": ${requirements.includes('3 complete outfit') ? '✅ YES' : '❌ NO'}`);
  console.log(`   Contains "shirt, pants, and shoes": ${requirements.includes('shirt, pants, and shoes') ? '✅ YES' : '❌ NO'}`);
}

console.log('\n🎉 Summary:');
console.log('✅ Removed all bold (**) formatting');
console.log('✅ Removed all underline (__) formatting'); 
console.log('✅ Removed all emoji headers');
console.log('✅ Clean text-only prompt for better AI parsing');
console.log('✅ All required sections present');

console.log('\n🚀 Prompt is now clean and ready for Gemini AI!');
