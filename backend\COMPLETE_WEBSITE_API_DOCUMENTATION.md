# 🌐 Complete Face App API Documentation for Website Development

const API_BASE_URL = 'https://faceapp-ttwh.onrender.com/api';

// Default headers for all requests
const defaultHeaders = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
};

// Authenticated headers
const authHeaders = (token) => ({
  ...defaultHeaders,
  'Authorization': `Bearer ${token}`
});
```

### **Health Check**
```javascript
// Test if API is running
fetch(`${API_BASE_URL}/health`)
  .then(response => response.json())
  .then(data => console.log('API Status:', data));
```

---

## 🔐 Authentication System

### **1. User Registration**

**Endpoint:** `POST /api/auth/register`

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "MyPassword123",
  "gender": "male"
}
```

**Validation Rules:**
- `name`: 2-50 characters, required
- `email`: Valid email format, unique, required
- `password`: Min 6 chars, must contain uppercase + lowercase + number
- `gender`: One of: `male`, `female`, `other`, `prefer_not_to_say`

**Success Response (201):**
```json
{
  "success": true,
  "message": "User registered successfully. Please check your email for the verification OTP.",
  "user": {
    "id": "64f1234567890abcdef12345",
    "name": "John Doe",
    "email": "<EMAIL>",
    "isEmailVerified": false
  }
}
```

**Frontend Example:**
```javascript
const registerUser = async (userData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: defaultHeaders,
      body: JSON.stringify(userData)
    });
    
    const result = await response.json();
    
    if (result.success) {
      // Show OTP verification form
      showOTPVerification(userData.email);
    } else {
      // Handle registration errors
      showError(result.message);
    }
  } catch (error) {
    console.error('Registration failed:', error);
  }
};
```

### **2. OTP Verification**

**Endpoint:** `POST /api/auth/verify-email-otp`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Email verified successfully",
  "data": {
    "email": "<EMAIL>",
    "isEmailVerified": true,
    "message": "Your account is now active"
  }
}
```

**Frontend Example:**
```javascript
const verifyOTP = async (email, otp) => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/verify-email-otp`, {
      method: 'POST',
      headers: defaultHeaders,
      body: JSON.stringify({ email, otp })
    });
    
    const result = await response.json();
    
    if (result.success) {
      // Redirect to login page
      window.location.href = '/login';
    } else {
      showError(result.message);
    }
  } catch (error) {
    console.error('OTP verification failed:', error);
  }
};
```

### **3. User Login**

**Endpoint:** `POST /api/auth/login`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "MyPassword123"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Login successful",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "64f1234567890abcdef12345",
    "name": "John Doe",
    "email": "<EMAIL>",
    "gender": "male",
    "isEmailVerified": true,
    "lastLogin": "2024-01-01T12:00:00.000Z",
    "createdAt": "2024-01-01T10:00:00.000Z"
  }
}
```

**Frontend Example:**
```javascript
const loginUser = async (email, password) => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: defaultHeaders,
      body: JSON.stringify({ email, password })
    });
    
    const result = await response.json();
    
    if (result.success) {
      // Store token in localStorage
      localStorage.setItem('authToken', result.token);
      localStorage.setItem('user', JSON.stringify(result.user));
      
      // Redirect to dashboard
      window.location.href = '/dashboard';
    } else {
      showError(result.message);
    }
  } catch (error) {
    console.error('Login failed:', error);
  }
};
```

### **4. Get User Profile**

**Endpoint:** `GET /api/auth/me`

**Headers:** `Authorization: Bearer <token>`

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "64f1234567890abcdef12345",
    "name": "John Doe",
    "email": "<EMAIL>",
    "gender": "male",
    "isEmailVerified": true,
    "lastLogin": "2024-01-01T12:00:00.000Z",
    "createdAt": "2024-01-01T10:00:00.000Z"
  }
}
```

**Frontend Example:**
```javascript
const getUserProfile = async () => {
  const token = localStorage.getItem('authToken');
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/me`, {
      method: 'GET',
      headers: authHeaders(token)
    });
    
    const result = await response.json();
    
    if (result.success) {
      return result.data;
    } else {
      // Token might be expired
      handleAuthError();
    }
  } catch (error) {
    console.error('Failed to get profile:', error);
  }
};
```

### **5. Logout**

**Endpoint:** `POST /api/auth/logout`

**Headers:** `Authorization: Bearer <token>`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

**Frontend Example:**
```javascript
const logoutUser = async () => {
  const token = localStorage.getItem('authToken');
  
  try {
    await fetch(`${API_BASE_URL}/auth/logout`, {
      method: 'POST',
      headers: authHeaders(token)
    });
    
    // Clear local storage
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    
    // Redirect to login
    window.location.href = '/login';
  } catch (error) {
    console.error('Logout failed:', error);
  }
};
```

### **6. Resend OTP**

**Endpoint:** `POST /api/auth/resend-verification`

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Verification OTP sent successfully"
}
```

### **7. Forgot Password**

**Endpoint:** `POST /api/auth/forgot-password`

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Password reset instructions sent to your email"
}
```

### **8. Reset Password**

**Endpoint:** `POST /api/auth/reset-password`

**Request Body:**
```json
{
  "token": "reset_token_from_email",
  "password": "NewPassword123"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

---

## 📸 Face Analysis APIs

### **1. Analyze Face from Direct Upload**

**Endpoint:** `POST /api/face/analyze-direct`

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "imageUrl": "https://res.cloudinary.com/dy1tsskkm/image/upload/v1234567890/face_analysis/abc123.jpg",
  "publicId": "face_analysis/abc123"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "analysis": {
      "_id": "64f1234567890abcdef12345",
      "userId": "64f1234567890abcdef12346",
      "imageUrl": "https://res.cloudinary.com/dy1tsskkm/image/upload/v1234567890/face_analysis/abc123.jpg",
      "faceDetected": true,
      "colors": {
        "dominantColors": ["#8B4513", "#D2691E", "#F5DEB3"],
        "skinTone": "#F5DEB3",
        "eyeColor": "#8B4513",
        "hairColor": "#654321"
      },
      "facialFeatures": {
        "faceShape": "oval",
        "eyeShape": "almond",
        "jawline": "soft",
        "cheekbones": "high"
      },
      "measurements": {
        "faceWidth": 180,
        "faceHeight": 240,
        "eyeDistance": 65,
        "noseWidth": 35
      },
      "confidence": 0.95,
      "processingTime": 2340,
      "createdAt": "2024-01-01T12:00:00.000Z"
    }
  }
}
```

**Frontend Example:**
```javascript
const analyzeFace = async (imageUrl, publicId) => {
  const token = localStorage.getItem('authToken');

  try {
    const response = await fetch(`${API_BASE_URL}/face/analyze-direct`, {
      method: 'POST',
      headers: authHeaders(token),
      body: JSON.stringify({ imageUrl, publicId })
    });

    const result = await response.json();

    if (result.success) {
      // Display analysis results
      displayAnalysisResults(result.data.analysis);
      return result.data.analysis;
    } else {
      showError(result.message);
    }
  } catch (error) {
    console.error('Face analysis failed:', error);
  }
};
```

### **2. Get Face Analysis History**

**Endpoint:** `GET /api/face/history?page=1&limit=10`

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "analyses": [
      {
        "_id": "64f1234567890abcdef12345",
        "imageUrl": "https://res.cloudinary.com/dy1tsskkm/image/upload/v1234567890/face_analysis/abc123.jpg",
        "faceDetected": true,
        "colors": {
          "dominantColors": ["#8B4513", "#D2691E"],
          "skinTone": "#F5DEB3"
        },
        "confidence": 0.95,
        "createdAt": "2024-01-01T12:00:00.000Z"
      }
    ],
    "pagination": {
      "current": 1,
      "pages": 3,
      "total": 25,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

**Frontend Example:**
```javascript
const getFaceAnalysisHistory = async (page = 1, limit = 10) => {
  const token = localStorage.getItem('authToken');

  try {
    const response = await fetch(`${API_BASE_URL}/face/history?page=${page}&limit=${limit}`, {
      method: 'GET',
      headers: authHeaders(token)
    });

    const result = await response.json();

    if (result.success) {
      displayAnalysisHistory(result.data.analyses);
      setupPagination(result.data.pagination);
      return result.data;
    }
  } catch (error) {
    console.error('Failed to get history:', error);
  }
};
```

### **3. Get Specific Face Analysis**

**Endpoint:** `GET /api/face/analysis/:id`

**Headers:** `Authorization: Bearer <token>`

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "_id": "64f1234567890abcdef12345",
    "userId": "64f1234567890abcdef12346",
    "imageUrl": "https://res.cloudinary.com/dy1tsskkm/image/upload/v1234567890/face_analysis/abc123.jpg",
    "faceDetected": true,
    "colors": {
      "dominantColors": ["#8B4513", "#D2691E", "#F5DEB3"],
      "skinTone": "#F5DEB3",
      "eyeColor": "#8B4513",
      "hairColor": "#654321"
    },
    "facialFeatures": {
      "faceShape": "oval",
      "eyeShape": "almond",
      "jawline": "soft",
      "cheekbones": "high"
    },
    "measurements": {
      "faceWidth": 180,
      "faceHeight": 240,
      "eyeDistance": 65,
      "noseWidth": 35
    },
    "confidence": 0.95,
    "processingTime": 2340,
    "createdAt": "2024-01-01T12:00:00.000Z"
  }
}
```

### **4. Delete Face Analysis**

**Endpoint:** `DELETE /api/face/analysis/:id`

**Headers:** `Authorization: Bearer <token>`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Face analysis deleted successfully"
}
```

---

## 🎨 Color Recommendation APIs

### **1. Get Color Recommendations**

**Endpoint:** `POST /api/face/analysis/:id/recommendations`

**Headers:** `Authorization: Bearer <token>`

**Request Body (Optional):**
```json
{
  "preferences": {
    "style": "professional",
    "occasion": "business",
    "season": "autumn"
  }
}
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "_id": "64f1234567890abcdef12347",
    "faceAnalysisId": "64f1234567890abcdef12345",
    "recommendations": [
      {
        "outfitName": "Professional Business Look",
        "shirt": {
          "color": "Navy Blue",
          "hex": "#000080",
          "reason": "Complements your warm skin tone"
        },
        "pants": {
          "color": "Charcoal Gray",
          "hex": "#36454F",
          "reason": "Classic neutral that works with navy"
        },
        "shoes": {
          "color": "Brown Leather",
          "hex": "#8B4513",
          "reason": "Matches your eye color beautifully"
        },
        "overallReason": "This combination enhances your natural coloring"
      }
    ],
    "colorPalette": {
      "bestColors": ["#000080", "#8B4513", "#228B22"],
      "avoidColors": ["#FF69B4", "#00FFFF"],
      "seasonalType": "Autumn"
    },
    "generalAdvice": "Your warm undertones work best with earth tones and deep colors.",
    "confidence": 0.9,
    "aiService": "gemini",
    "createdAt": "2024-01-01T12:30:00.000Z"
  }
}
```

**Frontend Example:**
```javascript
const getColorRecommendations = async (analysisId, preferences = {}) => {
  const token = localStorage.getItem('authToken');

  try {
    const response = await fetch(`${API_BASE_URL}/face/analysis/${analysisId}/recommendations`, {
      method: 'POST',
      headers: authHeaders(token),
      body: JSON.stringify({ preferences })
    });

    const result = await response.json();

    if (result.success) {
      displayColorRecommendations(result.data);
      return result.data;
    } else {
      showError(result.message);
    }
  } catch (error) {
    console.error('Failed to get recommendations:', error);
  }
};
```

### **2. Get Most Recent Recommendation**

**Endpoint:** `GET /api/face/recommendations/latest`

**Headers:** `Authorization: Bearer <token>`

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "_id": "64f1234567890abcdef12347",
    "faceAnalysisId": {
      "_id": "64f1234567890abcdef12345",
      "colors": {
        "dominantColors": ["#8B4513", "#D2691E"],
        "skinTone": "#F5DEB3"
      },
      "createdAt": "2024-01-01T12:00:00.000Z"
    },
    "recommendations": [
      {
        "outfitName": "Professional Business Look",
        "shirt": {
          "color": "Navy Blue",
          "hex": "#000080",
          "reason": "Complements your warm skin tone"
        }
      }
    ],
    "colorPalette": {
      "bestColors": ["#000080", "#8B4513"],
      "avoidColors": ["#FF69B4", "#00FFFF"],
      "seasonalType": "Autumn"
    },
    "generalAdvice": "Your warm undertones work best with earth tones.",
    "createdAt": "2024-01-01T12:30:00.000Z"
  }
}
```

### **3. Get Recommendation History**

**Endpoint:** `GET /api/face/recommendations/history?limit=10`

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `limit`: Number of recommendations to return (default: 10)

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "recommendations": [
      {
        "_id": "64f1234567890abcdef12347",
        "faceAnalysisId": {
          "_id": "64f1234567890abcdef12345",
          "colors": {
            "dominantColors": ["#8B4513", "#D2691E"],
            "skinTone": "#F5DEB3"
          },
          "createdAt": "2024-01-01T12:00:00.000Z"
        },
        "recommendations": [
          {
            "outfitName": "Professional Business Look"
          }
        ],
        "colorPalette": {
          "seasonalType": "Autumn"
        },
        "userRating": 5,
        "createdAt": "2024-01-01T12:30:00.000Z"
      }
    ],
    "count": 5
  }
}
```

### **4. Add Recommendation Feedback**

**Endpoint:** `POST /api/face/recommendations/:id/feedback`

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "rating": 5,
  "feedback": "Great color recommendations! Love the combinations.",
  "favoriteOutfits": [0, 1]
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Feedback added successfully",
  "data": {
    "rating": 5,
    "feedback": "Great color recommendations! Love the combinations.",
    "favoriteOutfits": [0, 1]
  }
}
```

---

## 📤 File Upload APIs

### **1. Get Upload Configuration**

**Endpoint:** `GET /api/upload/config`

**Headers:** `Authorization: Bearer <token>`

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "cloudName": "dy1tsskkm",
    "uploadPreset": "face_analysis",
    "maxFileSize": 10485760,
    "allowedFormats": ["jpg", "jpeg", "png", "gif", "bmp", "webp"],
    "folder": "face_analysis",
    "transformation": {
      "quality": "auto",
      "fetch_format": "auto"
    }
  }
}
```

### **2. Generate Upload Signature**

**Endpoint:** `POST /api/upload/signature`

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "folder": "face_analysis",
  "transformation": "c_fill,w_800,h_800,q_auto"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "signature": "abc123def456...",
    "timestamp": 1640995200,
    "cloudName": "dy1tsskkm",
    "apiKey": "123456789012345",
    "folder": "face_analysis",
    "uploadUrl": "https://api.cloudinary.com/v1_1/dy1tsskkm/image/upload"
  }
}
```

### **3. Mobile Upload Signature (Simplified)**

**Endpoint:** `POST /api/upload/mobile-signature`

**Headers:** `Authorization: Bearer <token>`

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "signature": "abc123def456...",
    "timestamp": 1640995200,
    "cloudName": "dy1tsskkm",
    "apiKey": "123456789012345",
    "uploadUrl": "https://api.cloudinary.com/v1_1/dy1tsskkm/image/upload",
    "folder": "face_analysis"
  }
}
```

**Frontend Upload Example:**
```javascript
const uploadImageToCloudinary = async (file) => {
  const token = localStorage.getItem('authToken');

  try {
    // Get upload signature
    const signatureResponse = await fetch(`${API_BASE_URL}/upload/mobile-signature`, {
      method: 'POST',
      headers: authHeaders(token)
    });

    const signatureData = await signatureResponse.json();

    if (!signatureData.success) {
      throw new Error('Failed to get upload signature');
    }

    // Prepare form data for Cloudinary
    const formData = new FormData();
    formData.append('file', file);
    formData.append('signature', signatureData.data.signature);
    formData.append('timestamp', signatureData.data.timestamp);
    formData.append('api_key', signatureData.data.apiKey);
    formData.append('folder', signatureData.data.folder);

    // Upload to Cloudinary
    const uploadResponse = await fetch(signatureData.data.uploadUrl, {
      method: 'POST',
      body: formData
    });

    const uploadResult = await uploadResponse.json();

    if (uploadResult.secure_url) {
      // Now analyze the uploaded image
      const analysis = await analyzeFace(uploadResult.secure_url, uploadResult.public_id);
      return { uploadResult, analysis };
    } else {
      throw new Error('Upload failed');
    }
  } catch (error) {
    console.error('Upload failed:', error);
    throw error;
  }
};
```

---

## 📊 Monitoring & Health APIs

### **1. Health Check**

**Endpoint:** `GET /api/health`

**No Authentication Required**

**Success Response (200):**
```json
{
  "success": true,
  "message": "Server is running successfully",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "performance": {
    "requestCount": 1234,
    "averageResponseTime": 120,
    "errorRate": 0.02
  },
  "cache": {
    "status": "healthy",
    "memoryCache": true,
    "redisCache": false
  },
  "uptime": 3600,
  "memory": {
    "used": "120 MB",
    "total": "512 MB"
  }
}
```

### **2. Simple Ping**

**Endpoint:** `GET /ping`

**No Authentication Required**

**Success Response (200):**
```json
{
  "success": true,
  "message": "pong",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### **3. Performance Metrics**

**Endpoint:** `GET /api/metrics`

**No Authentication Required**

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "requests": {
      "total": 1234,
      "successful": 1200,
      "failed": 34,
      "averageResponseTime": 120
    },
    "endpoints": {
      "/api/auth/login": {
        "count": 150,
        "averageTime": 80
      },
      "/api/face/analyze-direct": {
        "count": 45,
        "averageTime": 2500
      }
    },
    "errors": {
      "4xx": 20,
      "5xx": 14
    },
    "uptime": 3600
  }
}
```

---

## 🛡️ Security & Rate Limiting

### **Rate Limits**

| Endpoint Type | Limit | Window | Description |
|---------------|-------|--------|-------------|
| Global | 2000 requests | 15 minutes | All endpoints |
| Registration | 5 requests | 1 minute | User registration |
| Login | 10 requests | 1 minute | User login |
| Authentication | 50 requests | 15 minutes | Auth endpoints |
| Face Analysis | 20 requests | 1 hour | Face analysis |
| Color Recommendations | 30 requests | 1 hour | AI recommendations |
| File Upload | 50 requests | 1 hour | File uploads |

### **Rate Limit Headers**

All responses include rate limiting headers:
```
X-RateLimit-Limit: 2000
X-RateLimit-Remaining: 1995
X-RateLimit-Reset: 2024-01-01T12:15:00.000Z
```

### **Rate Limit Error Response (429):**
```json
{
  "success": false,
  "message": "Too many requests from this IP, please try again later",
  "retryAfter": 900,
  "limit": 2000,
  "current": 2001,
  "ip": "*************"
}
```

---

## 💻 Frontend Integration Examples

### **Complete Authentication Flow**

```html
<!DOCTYPE html>
<html>
<head>
    <title>Face App</title>
    <style>
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; }
        .form-group input, .form-group select { width: 100%; padding: 8px; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        .error { color: red; margin-top: 10px; }
        .success { color: green; margin-top: 10px; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Registration Form -->
        <div id="registerForm">
            <h2>Register</h2>
            <form onsubmit="handleRegister(event)">
                <div class="form-group">
                    <label>Name:</label>
                    <input type="text" id="name" required>
                </div>
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" id="email" required>
                </div>
                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" id="password" required>
                </div>
                <div class="form-group">
                    <label>Gender:</label>
                    <select id="gender" required>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                        <option value="prefer_not_to_say">Prefer not to say</option>
                    </select>
                </div>
                <button type="submit" class="btn">Register</button>
            </form>
            <div id="registerMessage"></div>
        </div>

        <!-- OTP Verification Form -->
        <div id="otpForm" class="hidden">
            <h2>Verify Your Email</h2>
            <p>Enter the 6-digit code sent to your email</p>
            <form onsubmit="handleOTPVerification(event)">
                <div class="form-group">
                    <label>OTP Code:</label>
                    <input type="text" id="otp" maxlength="6" required>
                </div>
                <button type="submit" class="btn">Verify</button>
                <button type="button" class="btn" onclick="resendOTP()">Resend OTP</button>
            </form>
            <div id="otpMessage"></div>
        </div>

        <!-- Login Form -->
        <div id="loginForm" class="hidden">
            <h2>Login</h2>
            <form onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" id="loginEmail" required>
                </div>
                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" id="loginPassword" required>
                </div>
                <button type="submit" class="btn">Login</button>
            </form>
            <div id="loginMessage"></div>
        </div>

        <!-- Dashboard -->
        <div id="dashboard" class="hidden">
            <h2>Dashboard</h2>
            <div id="userInfo"></div>
            <button class="btn" onclick="showUploadForm()">Analyze Face</button>
            <button class="btn" onclick="showHistory()">View History</button>
            <button class="btn" onclick="logout()">Logout</button>

            <!-- Face Analysis Upload -->
            <div id="uploadForm" class="hidden">
                <h3>Upload Face Image</h3>
                <input type="file" id="faceImage" accept="image/*" onchange="handleImageUpload(event)">
                <div id="uploadMessage"></div>
            </div>

            <!-- Analysis Results -->
            <div id="analysisResults" class="hidden">
                <h3>Analysis Results</h3>
                <div id="analysisData"></div>
                <button class="btn" onclick="getColorRecommendations()">Get Color Recommendations</button>
            </div>

            <!-- Color Recommendations -->
            <div id="colorRecommendations" class="hidden">
                <h3>Color Recommendations</h3>
                <div id="recommendationsData"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://faceapp-ttwh.onrender.com/api';
        let currentUser = null;
        let currentAnalysis = null;
        let userEmail = '';

        // Utility functions
        const showElement = (id) => document.getElementById(id).classList.remove('hidden');
        const hideElement = (id) => document.getElementById(id).classList.add('hidden');
        const showMessage = (elementId, message, isError = false) => {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = isError ? 'error' : 'success';
        };

        // Authentication functions
        async function handleRegister(event) {
            event.preventDefault();

            const userData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                gender: document.getElementById('gender').value
            };

            userEmail = userData.email;

            try {
                const response = await fetch(`${API_BASE_URL}/auth/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(userData)
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('registerMessage', result.message);
                    hideElement('registerForm');
                    showElement('otpForm');
                } else {
                    showMessage('registerMessage', result.message, true);
                }
            } catch (error) {
                showMessage('registerMessage', 'Registration failed. Please try again.', true);
            }
        }

        async function handleOTPVerification(event) {
            event.preventDefault();

            const otp = document.getElementById('otp').value;

            try {
                const response = await fetch(`${API_BASE_URL}/auth/verify-email-otp`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: userEmail, otp })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('otpMessage', 'Email verified successfully!');
                    setTimeout(() => {
                        hideElement('otpForm');
                        showElement('loginForm');
                    }, 2000);
                } else {
                    showMessage('otpMessage', result.message, true);
                }
            } catch (error) {
                showMessage('otpMessage', 'Verification failed. Please try again.', true);
            }
        }

        async function resendOTP() {
            try {
                const response = await fetch(`${API_BASE_URL}/auth/resend-verification`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: userEmail })
                });

                const result = await response.json();
                showMessage('otpMessage', result.message, !result.success);
            } catch (error) {
                showMessage('otpMessage', 'Failed to resend OTP.', true);
            }
        }

        async function handleLogin(event) {
            event.preventDefault();

            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password })
                });

                const result = await response.json();

                if (result.success) {
                    localStorage.setItem('authToken', result.token);
                    localStorage.setItem('user', JSON.stringify(result.user));
                    currentUser = result.user;

                    hideElement('loginForm');
                    showDashboard();
                } else {
                    showMessage('loginMessage', result.message, true);
                }
            } catch (error) {
                showMessage('loginMessage', 'Login failed. Please try again.', true);
            }
        }

        function showDashboard() {
            showElement('dashboard');
            document.getElementById('userInfo').innerHTML = `
                <h3>Welcome, ${currentUser.name}!</h3>
                <p>Email: ${currentUser.email}</p>
                <p>Member since: ${new Date(currentUser.createdAt).toLocaleDateString()}</p>
            `;
        }

        // Face analysis functions
        function showUploadForm() {
            showElement('uploadForm');
        }

        async function handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            showMessage('uploadMessage', 'Uploading and analyzing image...');

            try {
                const token = localStorage.getItem('authToken');

                // Get upload signature
                const signatureResponse = await fetch(`${API_BASE_URL}/upload/mobile-signature`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const signatureData = await signatureResponse.json();

                if (!signatureData.success) {
                    throw new Error('Failed to get upload signature');
                }

                // Upload to Cloudinary
                const formData = new FormData();
                formData.append('file', file);
                formData.append('signature', signatureData.data.signature);
                formData.append('timestamp', signatureData.data.timestamp);
                formData.append('api_key', signatureData.data.apiKey);
                formData.append('folder', signatureData.data.folder);

                const uploadResponse = await fetch(signatureData.data.uploadUrl, {
                    method: 'POST',
                    body: formData
                });

                const uploadResult = await uploadResponse.json();

                if (!uploadResult.secure_url) {
                    throw new Error('Upload failed');
                }

                // Analyze the uploaded image
                const analysisResponse = await fetch(`${API_BASE_URL}/face/analyze-direct`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        imageUrl: uploadResult.secure_url,
                        publicId: uploadResult.public_id
                    })
                });

                const analysisResult = await analysisResponse.json();

                if (analysisResult.success) {
                    currentAnalysis = analysisResult.data.analysis;
                    showMessage('uploadMessage', 'Analysis completed successfully!');
                    displayAnalysisResults(currentAnalysis);
                } else {
                    showMessage('uploadMessage', analysisResult.message, true);
                }
            } catch (error) {
                showMessage('uploadMessage', 'Upload failed. Please try again.', true);
            }
        }

        function displayAnalysisResults(analysis) {
            showElement('analysisResults');
            document.getElementById('analysisData').innerHTML = `
                <img src="${analysis.imageUrl}" style="max-width: 300px; height: auto;">
                <h4>Face Analysis Results</h4>
                <p><strong>Face Detected:</strong> ${analysis.faceDetected ? 'Yes' : 'No'}</p>
                <p><strong>Confidence:</strong> ${(analysis.confidence * 100).toFixed(1)}%</p>
                <p><strong>Face Shape:</strong> ${analysis.facialFeatures?.faceShape || 'N/A'}</p>
                <p><strong>Eye Shape:</strong> ${analysis.facialFeatures?.eyeShape || 'N/A'}</p>
                <p><strong>Skin Tone:</strong> <span style="background-color: ${analysis.colors?.skinTone}; padding: 2px 8px; color: white;">${analysis.colors?.skinTone}</span></p>
                <p><strong>Eye Color:</strong> <span style="background-color: ${analysis.colors?.eyeColor}; padding: 2px 8px; color: white;">${analysis.colors?.eyeColor}</span></p>
                <p><strong>Hair Color:</strong> <span style="background-color: ${analysis.colors?.hairColor}; padding: 2px 8px; color: white;">${analysis.colors?.hairColor}</span></p>
            `;
        }

        async function getColorRecommendations() {
            if (!currentAnalysis) return;

            const token = localStorage.getItem('authToken');

            try {
                const response = await fetch(`${API_BASE_URL}/face/analysis/${currentAnalysis._id}/recommendations`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        preferences: {
                            style: 'professional',
                            occasion: 'business'
                        }
                    })
                });

                const result = await response.json();

                if (result.success) {
                    displayColorRecommendations(result.data);
                } else {
                    alert('Failed to get color recommendations: ' + result.message);
                }
            } catch (error) {
                alert('Failed to get color recommendations. Please try again.');
            }
        }

        function displayColorRecommendations(recommendations) {
            showElement('colorRecommendations');

            let html = `
                <h4>Your Color Recommendations</h4>
                <p><strong>Seasonal Type:</strong> ${recommendations.colorPalette.seasonalType}</p>
                <p><strong>General Advice:</strong> ${recommendations.generalAdvice}</p>

                <h5>Best Colors for You:</h5>
                <div style="display: flex; gap: 10px; margin-bottom: 20px;">
            `;

            recommendations.colorPalette.bestColors.forEach(color => {
                html += `<div style="width: 40px; height: 40px; background-color: ${color}; border: 1px solid #ccc;" title="${color}"></div>`;
            });

            html += `</div><h5>Colors to Avoid:</h5><div style="display: flex; gap: 10px; margin-bottom: 20px;">`;

            recommendations.colorPalette.avoidColors.forEach(color => {
                html += `<div style="width: 40px; height: 40px; background-color: ${color}; border: 1px solid #ccc;" title="${color}"></div>`;
            });

            html += `</div><h5>Outfit Recommendations:</h5>`;

            recommendations.recommendations.forEach((outfit, index) => {
                html += `
                    <div style="border: 1px solid #ddd; padding: 15px; margin-bottom: 15px;">
                        <h6>${outfit.outfitName}</h6>
                        <p><strong>Shirt:</strong> ${outfit.shirt.color} (${outfit.shirt.hex}) - ${outfit.shirt.reason}</p>
                        <p><strong>Pants:</strong> ${outfit.pants.color} (${outfit.pants.hex}) - ${outfit.pants.reason}</p>
                        <p><strong>Shoes:</strong> ${outfit.shoes.color} (${outfit.shoes.hex}) - ${outfit.shoes.reason}</p>
                        <p><strong>Why this works:</strong> ${outfit.overallReason}</p>
                    </div>
                `;
            });

            document.getElementById('recommendationsData').innerHTML = html;
        }

        async function showHistory() {
            const token = localStorage.getItem('authToken');

            try {
                const response = await fetch(`${API_BASE_URL}/face/history`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    displayHistory(result.data.analyses);
                } else {
                    alert('Failed to get history: ' + result.message);
                }
            } catch (error) {
                alert('Failed to get history. Please try again.');
            }
        }

        function displayHistory(analyses) {
            let html = '<h3>Your Analysis History</h3>';

            if (analyses.length === 0) {
                html += '<p>No analyses found.</p>';
            } else {
                analyses.forEach(analysis => {
                    html += `
                        <div style="border: 1px solid #ddd; padding: 15px; margin-bottom: 15px;">
                            <img src="${analysis.imageUrl}" style="max-width: 150px; height: auto; float: left; margin-right: 15px;">
                            <p><strong>Date:</strong> ${new Date(analysis.createdAt).toLocaleDateString()}</p>
                            <p><strong>Face Detected:</strong> ${analysis.faceDetected ? 'Yes' : 'No'}</p>
                            <p><strong>Confidence:</strong> ${(analysis.confidence * 100).toFixed(1)}%</p>
                            <button class="btn" onclick="viewAnalysis('${analysis._id}')">View Details</button>
                            <div style="clear: both;"></div>
                        </div>
                    `;
                });
            }

            document.getElementById('analysisResults').innerHTML = html;
            showElement('analysisResults');
        }

        async function viewAnalysis(analysisId) {
            const token = localStorage.getItem('authToken');

            try {
                const response = await fetch(`${API_BASE_URL}/face/analysis/${analysisId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    currentAnalysis = result.data;
                    displayAnalysisResults(currentAnalysis);
                } else {
                    alert('Failed to get analysis details: ' + result.message);
                }
            } catch (error) {
                alert('Failed to get analysis details. Please try again.');
            }
        }

        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            currentUser = null;
            currentAnalysis = null;

            hideElement('dashboard');
            hideElement('uploadForm');
            hideElement('analysisResults');
            hideElement('colorRecommendations');
            showElement('loginForm');
        }

        // Check if user is already logged in
        window.onload = function() {
            const token = localStorage.getItem('authToken');
            const user = localStorage.getItem('user');

            if (token && user) {
                currentUser = JSON.parse(user);
                hideElement('registerForm');
                showDashboard();
            }
        };
    </script>
</body>
</html>
```

---

## 🚨 Error Handling

### **Common Error Responses**

#### **Authentication Error (401):**
```json
{
  "success": false,
  "message": "Not authorized to access this route"
}
```

#### **Validation Error (400):**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "msg": "Password must be at least 6 characters long",
      "param": "password",
      "location": "body"
    }
  ]
}
```

#### **Not Found Error (404):**
```json
{
  "success": false,
  "message": "Resource not found"
}
```

#### **Server Error (500):**
```json
{
  "success": false,
  "message": "Internal Server Error"
}
```

### **Error Handling in Frontend**

```javascript
const handleApiError = (error, response) => {
  if (response.status === 401) {
    // Token expired or invalid
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    window.location.href = '/login';
  } else if (response.status === 429) {
    // Rate limit exceeded
    alert('Too many requests. Please wait before trying again.');
  } else if (response.status >= 500) {
    // Server error
    alert('Server error. Please try again later.');
  } else {
    // Other errors
    alert(error.message || 'An error occurred');
  }
};

// Usage in API calls
const apiCall = async (url, options) => {
  try {
    const response = await fetch(url, options);
    const data = await response.json();

    if (!response.ok) {
      handleApiError(data, response);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Network error:', error);
    alert('Network error. Please check your connection.');
    return null;
  }
};
```

---

## 📱 Response Formats

### **Standard Success Response**
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

### **Standard Error Response**
```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    // Validation errors array (if applicable)
  ]
}
```

### **Pagination Response**
```json
{
  "success": true,
  "data": {
    "items": [
      // Array of items
    ],
    "pagination": {
      "current": 1,
      "pages": 5,
      "total": 50,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
